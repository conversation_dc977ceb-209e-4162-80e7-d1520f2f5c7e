// Test script for translation agent functionality
// This file can be used to test the translation agent implementation

import { translationService } from './services/translationService';
import { transcriptionStorageService } from './services/transcriptionStorageService';

// Mock test data
const mockSceneData = {
  analysisId: 'test-analysis-123',
  sceneStart: 0,
  sceneDuration: 10
};

const mockTranscriptionSegments = [
  {
    start: 0,
    end: 3,
    text: "Hello, welcome to our video editing platform.",
    words: []
  },
  {
    start: 3,
    end: 6,
    text: "Today we'll show you how to use AI agents.",
    words: []
  },
  {
    start: 6,
    end: 10,
    text: "Let's start with subtitle generation.",
    words: []
  }
];

const mockTranscriptionMetadata = {
  language: 'en',
  duration: 10,
  word_count: 15,
  segment_count: 3,
  transcription_method: 'assemblyai',
  created_at: Date.now()
};

// Comprehensive test function
export async function testTranslationAgent() {
  console.log('🧪 Testing Translation Agent Implementation...');
  const testResults = {
    transcriptionStorage: false,
    translationService: false,
    languageSupport: false,
    timelineIntegration: false,
    chatCommands: false,
    apiIntegration: false
  };

  try {
    // Test 1: Transcription Storage
    console.log('\n📝 Test 1: Transcription Storage...');
    transcriptionStorageService.storeFullTranscription(
      mockSceneData.analysisId,
      mockTranscriptionSegments,
      mockTranscriptionMetadata
    );

    const hasTranscription = transcriptionStorageService.hasTranscription(mockSceneData.analysisId);
    const sceneSubtitles = transcriptionStorageService.getSceneSubtitles(
      mockSceneData.analysisId,
      mockSceneData.sceneStart,
      mockSceneData.sceneDuration
    );

    if (hasTranscription && sceneSubtitles.length > 0) {
      testResults.transcriptionStorage = true;
      console.log('✅ Transcription storage working correctly');
    } else {
      console.log('❌ Transcription storage failed');
    }

    // Test 2: Translation Service
    console.log('\n🌐 Test 2: Translation Service...');
    const translationOptions = {
      targetLanguage: 'es',
      sourceLanguage: 'en',
      includeSubtitles: true,
      includeDubbedAudio: true,
      voiceType: 'neutral' as const,
      speechRate: 1.0
    };

    const result = await translationService.translateScene(
      mockSceneData,
      translationOptions
    );

    if (result.originalSubtitles.length > 0 && result.translatedSubtitles.length > 0) {
      testResults.translationService = true;
      console.log('✅ Translation service working correctly');
      console.log(`   - Original: ${result.originalSubtitles.length} segments`);
      console.log(`   - Translated: ${result.translatedSubtitles.length} segments`);
      console.log(`   - Audio: ${result.dubbedAudioUrl ? 'Generated' : 'Not generated'}`);
      console.log(`   - Processing time: ${result.processingTime}ms`);
    } else {
      console.log('❌ Translation service failed');
    }

    // Test 3: Language Support
    console.log('\n🔧 Test 3: Language Support...');
    const supportedLanguages = translationService.getSupportedLanguages();
    const testLanguages = ['fr', 'de', 'it', 'pt', 'ru'];
    let languageTestsPassed = 0;

    for (const lang of testLanguages) {
      try {
        const langResult = await translationService.translateScene(
          mockSceneData,
          { ...translationOptions, targetLanguage: lang }
        );
        if (langResult.translatedSubtitles.length > 0) {
          languageTestsPassed++;
          console.log(`   ✅ ${lang.toUpperCase()}: ${langResult.translatedSubtitles.length} segments`);
        }
      } catch (error) {
        console.log(`   ❌ ${lang.toUpperCase()}: Failed`);
      }
    }

    if (languageTestsPassed === testLanguages.length) {
      testResults.languageSupport = true;
      console.log('✅ Language support working correctly');
    } else {
      console.log(`❌ Language support partial (${languageTestsPassed}/${testLanguages.length})`);
    }

    // Test 4: Timeline Integration (mock test)
    console.log('\n🎬 Test 4: Timeline Integration...');
    try {
      // Import timeline integration service
      const { timelineIntegrationService } = await import('./services/timelineIntegrationService');

      // Mock AI processing result
      const mockAIResult = {
        result: {
          originalSubtitles: result.originalSubtitles,
          translatedSubtitles: result.translatedSubtitles,
          dubbedAudioUrl: result.dubbedAudioUrl,
          targetLanguage: result.targetLanguage,
          duration: result.duration
        },
        metadata: {
          agentType: 'translation-agent',
          processedBy: 'test'
        }
      };

      // Test timeline integration (this would normally be called automatically)
      console.log('   - Timeline integration service available');
      testResults.timelineIntegration = true;
      console.log('✅ Timeline integration ready');
    } catch (error) {
      console.log('❌ Timeline integration failed:', error);
    }

    // Test 5: Chat Commands (mock test)
    console.log('\n💬 Test 5: Chat Commands...');
    try {
      const { chatbotService } = await import('./services/chatbotService');

      const testCommands = [
        'Add translation agent to scene 1',
        'Translate video to Spanish',
        'Dub video in French'
      ];

      let commandTestsPassed = 0;
      for (const command of testCommands) {
        try {
          const response = await chatbotService.processMessage(command, {
            currentAnalysisData: {
              scenes: [{ sceneId: 'test-scene-1' }]
            }
          });

          if (response && !response.includes('not understand')) {
            commandTestsPassed++;
            console.log(`   ✅ "${command}": Recognized`);
          } else {
            console.log(`   ❌ "${command}": Not recognized`);
          }
        } catch (error) {
          console.log(`   ❌ "${command}": Error`);
        }
      }

      if (commandTestsPassed >= 2) {
        testResults.chatCommands = true;
        console.log('✅ Chat commands working correctly');
      } else {
        console.log('❌ Chat commands failed');
      }
    } catch (error) {
      console.log('❌ Chat commands test failed:', error);
    }

    // Test 6: API Integration Status
    console.log('\n🔑 Test 6: API Integration Status...');
    try {
      const { hasApiKey } = await import('./config/apiKeys');

      const googleCloudAvailable = hasApiKey('google-cloud');
      const assemblyAIAvailable = hasApiKey('assemblyai');

      console.log(`   - Google Cloud API: ${googleCloudAvailable ? '✅ Configured' : '❌ Not configured'}`);
      console.log(`   - AssemblyAI API: ${assemblyAIAvailable ? '✅ Configured' : '❌ Not configured'}`);

      if (googleCloudAvailable && assemblyAIAvailable) {
        testResults.apiIntegration = true;
        console.log('✅ API integration ready for production');
      } else {
        console.log('⚠️ API integration using mock services');
      }
    } catch (error) {
      console.log('❌ API integration test failed:', error);
    }

    // Final Results
    console.log('\n📊 Test Results Summary:');
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const totalTests = Object.keys(testResults).length;

    Object.entries(testResults).forEach(([test, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
    });

    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log('🎉 All translation agent tests passed! Ready for production.');
    } else if (passedTests >= totalTests * 0.8) {
      console.log('✅ Translation agent mostly working. Minor issues to address.');
    } else {
      console.log('⚠️ Translation agent needs attention. Several tests failed.');
    }

    return passedTests >= totalTests * 0.8;

  } catch (error) {
    console.error('❌ Translation agent test suite failed:', error);
    return false;
  } finally {
    // Cleanup
    transcriptionStorageService.removeTranscription(mockSceneData.analysisId);
    console.log('\n🧹 Test cleanup completed');
  }
}

// Quick integration test for browser console
export async function quickTranslationTest() {
  console.log('🚀 Quick Translation Agent Test...');

  try {
    // Test basic translation functionality
    const { translationService } = await import('./services/translationService');
    const { transcriptionStorageService } = await import('./services/transcriptionStorageService');

    // Store test data
    transcriptionStorageService.storeFullTranscription(
      'quick-test',
      [{ start: 0, end: 3, text: "Hello world", words: [] }],
      { language: 'en', duration: 3, word_count: 2, segment_count: 1, transcription_method: 'test', created_at: Date.now() }
    );

    // Test translation
    const result = await translationService.translateScene(
      { analysisId: 'quick-test', sceneStart: 0, sceneDuration: 3 },
      { targetLanguage: 'es', sourceLanguage: 'en', includeSubtitles: true, includeDubbedAudio: false }
    );

    // Cleanup
    transcriptionStorageService.removeTranscription('quick-test');

    if (result.translatedSubtitles.length > 0) {
      console.log('✅ Quick test passed! Translation agent is working.');
      console.log(`   Original: "${result.originalSubtitles[0]?.text}"`);
      console.log(`   Translated: "${result.translatedSubtitles[0]?.text}"`);
      return true;
    } else {
      console.log('❌ Quick test failed - no translations generated');
      return false;
    }

  } catch (error) {
    console.error('❌ Quick test failed:', error);
    return false;
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testTranslationAgent = testTranslationAgent;
  (window as any).quickTranslationTest = quickTranslationTest;
  console.log('🔧 Translation agent tests available:');
  console.log('   - window.testTranslationAgent() - Full test suite');
  console.log('   - window.quickTranslationTest() - Quick functionality test');
}
