// Debug script for translation agent issues
// Run this in browser console to diagnose and fix translation problems

export async function debugTranslationAgent() {
  console.log('🔍 Debugging Translation Agent...');
  
  try {
    // Check current project state
    const { projectDataManager } = await import('./utils/projectDataManager');
    const project = projectDataManager.getCurrentProject();
    
    console.log('📊 Current Project State:', {
      hasProject: !!project,
      analysisId: project?.analysisId,
      sceneCount: project?.scenes?.length || 0,
      fileName: project?.videoFileName
    });
    
    if (!project || !project.scenes || project.scenes.length === 0) {
      console.log('❌ No project or scenes found - upload a video first');
      return;
    }
    
    const scene = project.scenes[0];
    console.log('🎬 First Scene:', {
      sceneId: scene.sceneId,
      title: scene.title,
      start: scene.start,
      duration: scene.duration
    });
    
    // Check transcription storage
    const { transcriptionStorageService } = await import('./services/transcriptionStorageService');
    const hasTranscription = transcriptionStorageService.hasTranscription(project.analysisId);
    
    console.log('📝 Transcription Status:', {
      hasTranscription,
      metadata: hasTranscription ? transcriptionStorageService.getTranscriptionMetadata(project.analysisId) : null
    });
    
    if (hasTranscription) {
      const sceneSubtitles = transcriptionStorageService.getSceneSubtitles(
        project.analysisId,
        scene.start,
        scene.duration
      );
      console.log(`✅ Scene has ${sceneSubtitles.length} subtitle segments`);
    } else {
      console.log('⚠️ No transcription found - will use on-demand generation');
    }
    
    // Test translation service directly
    console.log('🌐 Testing Translation Service...');
    const { translationService } = await import('./services/translationService');
    
    const result = await translationService.translateScene(
      {
        analysisId: project.analysisId,
        sceneStart: scene.start,
        sceneDuration: scene.duration
      },
      {
        targetLanguage: 'es',
        sourceLanguage: 'en',
        includeSubtitles: true,
        includeDubbedAudio: false
      }
    );
    
    console.log('✅ Translation Result:', {
      originalCount: result.originalSubtitles.length,
      translatedCount: result.translatedSubtitles.length,
      method: result.method,
      confidence: result.confidence,
      processingTime: result.processingTime
    });
    
    if (result.originalSubtitles.length > 0) {
      console.log('📝 Sample Original:', result.originalSubtitles[0]);
    }
    
    if (result.translatedSubtitles.length > 0) {
      console.log('🌐 Sample Translation:', result.translatedSubtitles[0]);
    } else {
      console.log('❌ No translations generated');
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
    return null;
  }
}

export async function forceCreateMockTranscription() {
  console.log('🔧 Creating mock transcription for current project...');
  
  try {
    const { projectDataManager } = await import('./utils/projectDataManager');
    const { transcriptionStorageService } = await import('./services/transcriptionStorageService');
    
    const project = projectDataManager.getCurrentProject();
    if (!project) {
      console.log('❌ No current project found');
      return false;
    }
    
    // Create mock transcription segments
    const mockSegments = [
      {
        start: 0,
        end: 3,
        text: "Welcome to our video editing platform with AI agents.",
        words: []
      },
      {
        start: 3,
        end: 6,
        text: "This demonstration shows translation capabilities.",
        words: []
      },
      {
        start: 6,
        end: 8,
        text: "AI translation agents can process multiple languages.",
        words: []
      }
    ];
    
    const mockMetadata = {
      language: 'en',
      duration: 8,
      word_count: 20,
      segment_count: 3,
      transcription_method: 'mock-demo',
      created_at: Date.now()
    };
    
    // Store mock transcription
    transcriptionStorageService.storeFullTranscription(
      project.analysisId,
      mockSegments,
      mockMetadata
    );
    
    console.log('✅ Mock transcription created successfully');
    console.log('🔄 Now try the translation agent again');
    
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create mock transcription:', error);
    return false;
  }
}

export async function testTranslationAgentNow() {
  console.log('🚀 Testing Translation Agent with Current Setup...');
  
  // First create mock transcription if needed
  const { transcriptionStorageService } = await import('./services/transcriptionStorageService');
  const { projectDataManager } = await import('./utils/projectDataManager');
  
  const project = projectDataManager.getCurrentProject();
  if (!project) {
    console.log('❌ No project found');
    return false;
  }
  
  if (!transcriptionStorageService.hasTranscription(project.analysisId)) {
    console.log('📝 Creating mock transcription...');
    await forceCreateMockTranscription();
  }
  
  // Now test translation
  return await debugTranslationAgent();
}

// Export for browser console
if (typeof window !== 'undefined') {
  (window as any).debugTranslationAgent = debugTranslationAgent;
  (window as any).forceCreateMockTranscription = forceCreateMockTranscription;
  (window as any).testTranslationAgentNow = testTranslationAgentNow;
  
  console.log('🔧 Translation debug functions available:');
  console.log('   - window.debugTranslationAgent() - Diagnose current state');
  console.log('   - window.forceCreateMockTranscription() - Create demo transcription');
  console.log('   - window.testTranslationAgentNow() - Full test with setup');
}
